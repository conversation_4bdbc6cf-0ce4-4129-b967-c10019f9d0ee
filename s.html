<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<style>

  body{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    overflow-x: hidden;
    background: #000;
  }


  .narbar{
    position: fixed;
    width: 100vw;
    background: red;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 10rem;
    z-index: 100;
  }

  .logo{
    width: 40%;
    height: 1rem;
    background: blue;
  }

  .menu-btn{
    width: 30%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: right;
    background: rgb(0, 255, 115);
  }

  .menu-overlay{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;}



/* button */
.btn-cta-navbar {
    position: relative;
    display: inline-block;
    padding: 0.5rem 1.5rem;
    margin-top: 1rem;
    border-radius: 1000px;
    font-weight: 100;
    font-size:1.3em;
    line-height: 110%;
    text-transform: uppercase;
    transition: transform .3s;
    overflow: hidden;
    color: wheat;
    text-decoration: none;
  }
  
  
  .btn-cta-navbar:hover {
    transform: scaleX(1.02);
    transition: transform .6s cubic-bezier(.34, 5.56, .64, 1);
  }
  
  
  .btn-cta-border-navbar {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 1px;
    z-index: 3;
    border: 1px solid;
    border-radius: 1000px;
  }
  
  .btn-cta-ripple-navbar {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    transform: translateZ(0);
    border-radius: inherit;
  }
  
  .btn-cta-ripple-navbar span {
    display: block;
    width: 100%;
    height: 100%;
    transform: translateY(101%);
    background: currentColor;
    border-radius: 50% 50% 0 0;
    transition: transform .5s cubic-bezier(.4, 0, 0, 1), border-radius .5s cubic-bezier(.4, 0, 0, 1);
  }
  
  .btn-cta-navbar:hover .btn-cta-ripple-navbar span {
    border-radius: 0;
    transform: translateY(0);
    transition-duration: .5s, .9s;
  }
  
  .btn-cta-title-navbar {
    position: relative;
    display: block;
    padding: 0 .16em 0 0;
    overflow: hidden;
    z-index: 2;
  }
  
  .btn-cta-title-navbar span {
    display: block;
    transition: transform .8s cubic-bezier(.16, 1, .3, 1);
  }
  
  .btn-cta-title-navbar span:after {
    content: attr(data-text);
    display: block;
    position: absolute;
    top: 110%;
    left: 0;
    color: rgb(177, 55, 55);
  }
  
  .btn-cta-title-navbar span::selection{
    background: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .btn-cta-navbar:hover .btn-cta-title-navbar span {
    transform: translateY(-110%);
  }
  
</style>
<body>

  <div class="narbar">
    <div class="logo"></div>
    <div class="menu-btn">
           <a class="btn-cta-navbar" href="#">
                <span class="btn-cta-border-navbar"></span>
                <span class="btn-cta-ripple-navbar"><span></span></span>
                <span class="btn-cta-title-navbar"><span data-text="CLOSE">OPEN</span></span>
            </a>
    </div>
  </div>
  <div class="menu-overlay"></div>
  
</body>
</html>